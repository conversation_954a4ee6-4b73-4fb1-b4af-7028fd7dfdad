<script>
	import { onMount, onDestroy } from 'svelte';

	export let data;

	let downloadStatus = '';
	let isDownloading = false;

	onMount(() => {
		if (data && (data.url || data.secondaryUrl || data.primaryUrl)) {
			downloadFile();
		}
	});

	async function downloadFile() {
		isDownloading = true;

		// Handle legacy single URL format
		if (data.url) {
			downloadStatus = 'Starting download...';
			performDownload(data.url, data.download);
			return;
		}

		// Handle fallback format with secondary, primary, and burst URLs
		if (data.secondaryUrl) {
			downloadStatus = 'Trying secondary source...';
			const success = await tryDownload(data.secondaryUrl, data.download);

			if (!success && data.primaryUrl) {
				downloadStatus = 'Secondary source failed, trying primary source...';
				const primarySuccess = await tryDownload(data.primaryUrl, data.download);

				if (!primarySuccess && data.burstUrl) {
					downloadStatus = 'Primary source failed, trying burst source (1080p)...';
					const burstSuccess = await tryBurstDownload(data.burstUrl, data.download);

					if (!burstSuccess) {
						// If all network tests fail, try direct download anyway
						downloadStatus = 'All network tests failed, attempting direct download...';
						performDownload(data.primaryUrl, data.download);
						downloadStatus = 'Download attempt started (network test failed)';
					}
				} else if (!primarySuccess) {
					// If both network tests fail, try direct download anyway
					downloadStatus = 'Network tests failed, attempting direct download...';
					performDownload(data.primaryUrl, data.download);
					downloadStatus = 'Download attempt started (network test failed)';
				}
			}
		} else if (data.primaryUrl) {
			downloadStatus = 'Using primary source...';
			const success = await tryDownload(data.primaryUrl, data.download);

			if (!success && data.burstUrl) {
				downloadStatus = 'Primary source failed, trying burst source (1080p)...';
				const burstSuccess = await tryBurstDownload(data.burstUrl, data.download);

				if (!burstSuccess) {
					// If network test fails, try direct download anyway
					downloadStatus = 'Network test failed, attempting direct download...';
					performDownload(data.primaryUrl, data.download);
					downloadStatus = 'Download attempt started (network test failed)';
				}
			} else if (!success) {
				// If network test fails, try direct download anyway
				downloadStatus = 'Network test failed, attempting direct download...';
				performDownload(data.primaryUrl, data.download);
				downloadStatus = 'Download attempt started (network test failed)';
			}
		} else if (data.burstUrl) {
			downloadStatus = 'Using burst source (1080p)...';
			const success = await tryBurstDownload(data.burstUrl, data.download);

			if (!success) {
				// If network test fails, try direct download anyway
				downloadStatus = 'Network test failed, attempting direct download...';
				performBurstDownload(data.burstUrl, data.download);
				downloadStatus = 'Download attempt started (network test failed)';
			}
		}

		isDownloading = false;
	}

	async function tryDownload(url, filename) {
		try {
			// Test if the URL is accessible with a timeout
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

			let response;
			try {
				// Try HEAD request first
				response = await fetch(url, {
					method: 'HEAD',
					signal: controller.signal
				});
			} catch (headError) {
				// If HEAD fails, try GET with range header to minimize data transfer
				response = await fetch(url, {
					method: 'GET',
					headers: { 'Range': 'bytes=0-0' },
					signal: controller.signal
				});
			}

			clearTimeout(timeoutId);

			if (!response.ok && response.status !== 206) { // 206 is partial content for range requests
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			// If accessible, perform the download
			performDownload(url, filename);
			downloadStatus = 'Download started successfully!';
			return true;
		} catch (error) {
			console.error('Download failed:', error);

			if (error.name === 'AbortError') {
				downloadStatus = 'Download failed: Connection timeout';
			} else {
				downloadStatus = `Download failed: ${error.message}`;
			}
			return false;
		}
	}

	async function tryBurstDownload(burstUrl, filename) {
		try {
			// First get the actual download URL from the burst source
			const response = await fetch(`/api/watch/getBurstLink?link=${encodeURIComponent(burstUrl)}`);

			if (!response.ok) {
				throw new Error(`Burst source fetch failed: ${response.statusText}`);
			}

			const data = await response.json();
			if (!data.downloadUrl) {
				throw new Error('No burst download URL received');
			}

			// Test if the burst URL is accessible
			const testResponse = await fetch(data.downloadUrl, {
				method: 'HEAD',
				signal: AbortSignal.timeout(10000)
			});

			if (!testResponse.ok && testResponse.status !== 206) {
				throw new Error(`HTTP ${testResponse.status}: ${testResponse.statusText}`);
			}

			// If accessible, perform the download
			performBurstDownload(burstUrl, filename);
			downloadStatus = 'Burst download started successfully!';
			return true;
		} catch (error) {
			console.error('Burst download failed:', error);
			downloadStatus = `Burst download failed: ${error.message}`;
			return false;
		}
	}

	async function performBurstDownload(burstUrl, filename) {
		try {
			// Get the actual download URL from the burst source
			const response = await fetch(`/api/watch/getBurstLink?link=${encodeURIComponent(burstUrl)}`);
			const data = await response.json();

			if (data.downloadUrl) {
				// Create a temporary anchor element
				const a = document.createElement('a');
				a.href = data.downloadUrl;
				a.download = filename;

				// Append to the body, click, and remove (for compatibility)
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
			}
		} catch (error) {
			console.error('Error performing burst download:', error);
		}
	}

	function performDownload(url, filename) {
		// Create a temporary anchor element
		const a = document.createElement('a');
		a.href = url;
		a.download = filename;

		// Append to the body, click, and remove (for compatibility)
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);

		// Clean up after a short delay to ensure the download starts
		setTimeout(() => {
			if (data.cleanup) {
				data.cleanup();
			}
		}, 100);
	}

	// Clean up on component destruction
	onDestroy(() => {
		if (data && data.cleanup) {
			data.cleanup();
		}
	});
</script>

{#if data && (data.url || data.secondaryUrl || data.primaryUrl || data.burstUrl)}
	<div class="download-container">
		<p>File download for "{data.download}" should start automatically.</p>

		{#if downloadStatus}
			<p class="download-status" class:downloading={isDownloading}>
				{downloadStatus}
			</p>
		{/if}
	</div>
{:else if data && data.error}
	<div class="error-container">
		<p class="error">Error: {data.error}</p>
	</div>
{/if}

<style>
	.download-container {
		padding: 20px;
		text-align: center;
	}

	.download-status {
		margin-top: 10px;
		padding: 10px;
		border-radius: 4px;
		background-color: #f0f0f0;
		color: #333;
	}

	.download-status.downloading {
		background-color: #e3f2fd;
		color: #1976d2;
	}

	.error-container {
		padding: 20px;
		text-align: center;
	}

	.error {
		color: #d32f2f;
		background-color: #ffebee;
		padding: 10px;
		border-radius: 4px;
		border: 1px solid #ffcdd2;
	}
</style>

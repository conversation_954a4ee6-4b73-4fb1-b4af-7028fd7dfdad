import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

// Initialize Supabase client
const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

const BASE64_CHARS = '6HOXGZkuYQKba084JgjVy+9driFE7INcDUhL/Mmqx2WPBCS5p1slvReAwotf3znT';
const STANDARD_BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

async function getVideoLinksFromTitleAndEpisode(title, episode, quality) {
  title = title.replaceAll(' ', '_');
  const { data: mediaQuery, error: queryError } = await supabase
    .from('anime')
    .select(`
      primary_source,
      secondary_source,
      anilist_id,
      anime_title
    `)
    .eq('anime_title', title)
    .eq('episode_number', episode);

  if (queryError || !mediaQuery || mediaQuery.length === 0) return null;

  // Check if anime is hidden in metadata
  // const { data: metadataQuery, error: metadataError } = await supabase
  //   .from('anime_metadata')
  //   .select('hidden')
  //   .eq('anilist_id', mediaQuery[0].anilist_id)
  //   .single();

  // if (metadataError) {
  //   console.error('Error fetching metadata:', metadataError);
  // }

  // if (metadataQuery && metadataQuery.hidden === true) {
  //   return null;
  // }

  const qualityMap = {
    'source': 'Source',
    'mkv': 'SourceMKV',
    '1080p': 'FHD',
    '720p': 'HD',
    '480p': 'SD'
  };

  const secondaryLink = mediaQuery[0].secondary_source?.[qualityMap[quality]];
  const primaryLink = mediaQuery[0].primary_source?.[qualityMap[quality]];

  // Return both links for client-side fallback
  return {
    secondary: secondaryLink,
    primary: primaryLink,
    title: mediaQuery[0].anime_title
  };
}

async function getVideoLinksFromId(id, episode, quality) {
  // First check if anime is hidden in metadata
  // const { data: metadataQuery, error: metadataError } = await supabase
  //   .from('anime_metadata')
  //   .select('hidden')
  //   .eq('anilist_id', id)
  //   .single();

  // if (metadataError) {
  //   console.error('Error fetching metadata:', metadataError);
  // }

  // if (metadataQuery && metadataQuery.hidden === true) {
  //   return null;
  // }

  const { data: mediaQuery, error: queryError } = await supabase
    .from('anime')
    .select(`
      primary_source,
      secondary_source,
      anime_title
    `)
    .eq('anilist_id', id)
    .eq('episode_number', episode);

  if (queryError || !mediaQuery || mediaQuery.length === 0) return null;

  const qualityMap = {
    'source': 'Source',
    'mkv': 'SourceMKV',
    '1080p': 'FHD',
    '720p': 'HD',
    '480p': 'SD'
  };

  const secondaryLink = mediaQuery[0].secondary_source?.[qualityMap[quality]];
  const primaryLink = mediaQuery[0].primary_source?.[qualityMap[quality]];

  // Return both links for client-side fallback
  return {
    secondary: secondaryLink,
    primary: primaryLink,
    title: mediaQuery[0].anime_title
  };
}

async function getSubtitlesLinksFromTitleAndEpisode(title, episode, quality) {
  title = title.replaceAll(' ', '_');
  const { data: mediaQuery, error: queryError } = await supabase
    .from('anime')
    .select(`
      subtitleLinks, 
      is_encrypted,
      anilist_id,
      anime_title
    `)
    .eq('anime_title', title)
    .eq('episode_number', episode);

  if (queryError || !mediaQuery || mediaQuery.length === 0) return null;

  // Check if anime is hidden in metadata
  // const { data: metadataQuery, error: metadataError } = await supabase
  //   .from('anime_metadata')
  //   .select('hidden')
  //   .eq('anilist_id', mediaQuery[0].anilist_id)
  //   .single();

  // if (metadataError) {
  //   console.error('Error fetching metadata:', metadataError);
  // }

  // if (metadataQuery && metadataQuery.hidden === true) {
  //   return null;
  // }

  if (mediaQuery[0].is_encrypted) {
    const subtitleLink = quality === 'pl' ?
      mediaQuery[0].subtitleLinks.PL :
      mediaQuery[0].subtitleLinks.EN;

    const response = await fetch(subtitleLink);
    const encodedData = await response.text();
    const decryptedData = decodeCustom(encodedData);

    return downloadTextAsFile(
      decryptedData,
      `[lycoris.cafe] ${title} - ${episode}.ass`
    );
  }

  return quality === 'pl' ?
    mediaQuery[0].subtitleLinks.PL :
    mediaQuery[0].subtitleLinks.EN;
}

async function getSubtitlesLinksFromId(id, episode, quality) {
  // First check if anime is hidden in metadata
  // const { data: metadataQuery, error: metadataError } = await supabase
  //   .from('anime_metadata')
  //   .select('hidden')
  //   .eq('anilist_id', id)
  //   .single();

  // if (metadataError) {
  //   console.error('Error fetching metadata:', metadataError);
  // }

  // if (metadataQuery && metadataQuery.hidden === true) {
  //   return null;
  // }

  const { data: mediaQuery, error: queryError } = await supabase
    .from('anime')
    .select(`
      subtitleLinks, 
      is_encrypted,
      anime_title
    `)
    .eq('anilist_id', id)
    .eq('episode_number', episode);

  if (queryError || !mediaQuery || mediaQuery.length === 0) return null;

  const title = mediaQuery[0].anime_title;

  if (mediaQuery[0].is_encrypted) {
    const subtitleLink = quality === 'pl' ?
      mediaQuery[0].subtitleLinks.PL :
      mediaQuery[0].subtitleLinks.EN;

    const response = await fetch(subtitleLink);
    const encodedData = await response.text();
    const decryptedData = decodeCustom(encodedData);

    return downloadTextAsFile(
      decryptedData,
      `[lycoris.cafe] ${title} - ${episode}.ass`
    );
  }

  return {
    link: quality === 'pl' ? mediaQuery[0].subtitleLinks.PL : mediaQuery[0].subtitleLinks.EN,
    title: title
  };
}

function downloadTextAsFile(text, filename) {
  const blob = new Blob([text], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  return {
    url,
    download: filename,
    cleanup: () => URL.revokeObjectURL(url)
  };
}

function decodeCustom(encoded) {
  let decoded = encoded
    .split('')
    .map(char => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : BASE64_CHARS[(index - 3 + BASE64_CHARS.length) % BASE64_CHARS.length];
    })
    .join('');

  decoded = decoded.split('').reverse().join('');

  decoded = decoded
    .split('')
    .map(char => {
      const index = BASE64_CHARS.indexOf(char);
      return index === -1 ? char : STANDARD_BASE64_CHARS[index];
    })
    .join('');

  try {
    const binaryData = atob(decoded);
    const bytes = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      bytes[i] = binaryData.charCodeAt(i);
    }
    return new TextDecoder('utf-8').decode(bytes);
  } catch (e) {
    console.error('Decoding failed:', e);
    return 'Decoding failed. Invalid input.';
  }
}

export async function GET({ url }) {
  const animeTitle = url.searchParams.get('title');
  const animeId = url.searchParams.get('id');
  const animeEpisode = Number(url.searchParams.get('episode'));
  const quality = url.searchParams.get('q');

  if (!['source', '1080p', '720p', '480p', 'pl', 'en', 'mkv'].includes(quality)) {
    return new Response(null, {
      status: 302,
      headers: { Location: '/' }
    });
  }

  if ((!animeTitle && !animeId) || !animeEpisode || !quality) {
    return new Response(null, {
      status: 302,
      headers: { Location: '/' }
    });
  }

  // Special redirects for specific titles (only if title is provided)
  if (animeTitle) {
    if (animeTitle === 'Na_Nare_Hana_Nare' && animeEpisode === 1) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=Nanare_Hananare&episode=1&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === 'NieR:Automata_Ver1.1a_Part_2' && animeEpisode === 1) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=NieR_Automata_Ver1.1a_Part_2&episode=1&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === '2.5_Jigen_no_Ririsa' && animeEpisode === 1) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === '2.5-jigen_no_Ririsa' && animeEpisode === 13) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === '2.5-Jigen_no_Ririsa' && animeEpisode < 16) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=2-5_Jigen_no_Ririsa&episode=${animeEpisode}&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === '2.5-Jigen_no_Ririsa' && animeEpisode > 16) {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=2_5_Jigen_no_Ririsa&episode=${animeEpisode}&redirect=true&q=${quality}`
        }
      });
    }

    if (animeTitle === 'Monogatari_Series_Off_and_Monster_Season') {
      return new Response(null, {
        status: 302,
        headers: {
          Location: `https://www.lycoris.cafe/download?title=Monogatari_Series_Off_&_Monster_Season&episode=${animeEpisode}&redirect=true&q=${quality}`
        }
      });
    }
  }

  try {
    // Handle video downloads
    if (quality !== 'pl' && quality !== 'en') {
      let videoLinks;
      let displayTitle = animeTitle;

      if (animeId) {
        videoLinks = await getVideoLinksFromId(animeId, animeEpisode, quality);
      } else {
        videoLinks = await getVideoLinksFromTitleAndEpisode(animeTitle, animeEpisode, quality);
      }

      if (!videoLinks || (!videoLinks.secondary && !videoLinks.primary)) {
        return new Response(JSON.stringify({ error: 'Video download link not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      if (videoLinks.title) {
        displayTitle = videoLinks.title;
      }

      // Process URLs for different providers
      function processUrl(url) {
        if (!url) return null;

        if (url.includes('gofile')) {
          return url;
        } else if (!url.includes('pixeldrain')) {
          return url.replace('&dl=0', '&dl=1');
        } else {
          const lastPart = url.split('/').pop();
          return `https://pixeldrain.com/api/file/${lastPart}?download`;
        }
      }

      const secondaryUrl = processUrl(videoLinks.secondary);
      const primaryUrl = processUrl(videoLinks.primary);

      return json({
        secondaryUrl,
        primaryUrl,
        download: `${displayTitle}_${animeEpisode}_${quality}.${quality === 'mkv' ? 'mkv' : 'mp4'}`,
        type: 'video'
      });
    }

    // Handle subtitle downloads
    let subtitleDownloadLink;
    let displayTitle = animeTitle;

    if (animeId) {
      const result = await getSubtitlesLinksFromId(animeId, animeEpisode, quality);
      if (result) {
        if (result.url) {
          subtitleDownloadLink = result;
        } else {
          subtitleDownloadLink = result.link;
          displayTitle = result.title;
        }
      }
    } else {
      subtitleDownloadLink = await getSubtitlesLinksFromTitleAndEpisode(animeTitle, animeEpisode, quality);
    }

    if (!subtitleDownloadLink) {
      return new Response(JSON.stringify({ error: 'Subtitle download link not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!subtitleDownloadLink.url) {
      return json({
        url: `${subtitleDownloadLink}?download`,
        download: `[lycoris.cafe] ${displayTitle} - ${animeEpisode}.ass`,
        type: 'subtitle'
      });
    }

    return json({
      ...subtitleDownloadLink,
      type: 'subtitle'
    });

  } catch (err) {
    console.error('Error in download API:', err);
    return new Response(JSON.stringify({
      error: 'Failed to process download request',
      message: err.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}